<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_3978e36f6b8406991dd84ab7e58dfba5 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_3978e36f6b8406991dd84ab7e58dfba5" ></div>
        
</body>
<script>
    
    
            var map_3978e36f6b8406991dd84ab7e58dfba5 = L.map(
                "map_3978e36f6b8406991dd84ab7e58dfba5",
                {
                    center: [40.9699889, -77.7278831],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 8,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_8d0ba4ef8ca2d6a08d9f52b8529103bf = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_8d0ba4ef8ca2d6a08d9f52b8529103bf.addTo(map_3978e36f6b8406991dd84ab7e58dfba5);
        
    
            var marker_f5bb9882e606c7a094cdb5d4fa0fe304 = L.marker(
                [40.9699889, -77.7278831],
                {
}
            ).addTo(map_3978e36f6b8406991dd84ab7e58dfba5);
        
    
        var popup_f41c39d4c5fb29df25a4ffc5a27097a8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f523ae1b7d8a09a0c153f8fc14723534 = $(`<div id="html_f523ae1b7d8a09a0c153f8fc14723534" style="width: 100.0%; height: 100.0%;">Pennsylvania</div>`)[0];
                popup_f41c39d4c5fb29df25a4ffc5a27097a8.setContent(html_f523ae1b7d8a09a0c153f8fc14723534);
            
        

        marker_f5bb9882e606c7a094cdb5d4fa0fe304.bindPopup(popup_f41c39d4c5fb29df25a4ffc5a27097a8)
        ;

        
    
</script>
</html>